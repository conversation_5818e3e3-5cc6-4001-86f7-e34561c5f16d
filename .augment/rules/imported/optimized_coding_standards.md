---
alwaysApply: true
---

# LLVM Coding Standards for LLM Code Generation

## Core Principles
- **Golden Rule**: When modifying existing code, match the existing style for consistency
- **Primary Goal**: Maximize readability and maintainability
- **Standard**: Use C++17 features, avoid vendor-specific extensions
- **Portability**: Write standards-conforming, portable code

## Language Standards & Libraries

### C++ Standards
- Use C++17 standard features only
- Avoid RTTI and exceptions entirely
- Prefer C++-style casts (`static_cast`, `reinterpret_cast`, `const_cast`) over C-style casts
- Use `llvm_unreachable()` instead of `assert(0)` for unreachable code

### Library Preferences
- Prefer LLVM support libraries over standard library when available:
  - Use `llvm::DenseMap` instead of `std::map` or `std::unordered_map`
  - Use `llvm::SmallVector` instead of `std::vector`
  - Use `raw_ostream` instead of `std::ostream`
- Use `llvm::sort()` instead of `std::sort()` for deterministic behavior

## Code Formatting & Style

### Basic Formatting
- **Line Width**: 80 columns maximum
- **Indentation**: Use spaces, never tabs
- **Namespaces**: Do not indent namespace contents
- **Braces**: No braces for single-statement if/else/loop bodies

### Include Organization
Order includes as follows:
1. Main module header (for .cpp files)
2. LLVM project headers (most specific to least specific)
3. System headers
4. Sort each category lexicographically

### Whitespace Rules
- No trailing whitespace
- Space before parentheses in control flow (`if (`, `for (`, `while (`), not in function calls
- Format lambdas like code blocks with 2-space indent
- Format braced initializer lists like function calls

## Naming Conventions

### Case Rules
- **Types** (classes, structs, enums): `UpperCamelCase` (e.g., `TextFileReader`)
- **Variables**: `UpperCamelCase` (e.g., `Leader`, `Boats`)
- **Functions**: `lowerCamelCase` (e.g., `openFile()`, `isFoo()`)
- **Enumerators**: `UpperCamelCase` with enum prefix (e.g., `VK_Argument`)

### Naming Guidelines
- Use descriptive names, avoid abbreviations
- Function names should be verb phrases
- Variable names should be nouns
- Enum types should have `Kind` suffix when used as discriminators

## Best Practices

### Control Flow
- Use early exits and `continue` to reduce nesting
- Avoid `else` after `return`, `break`, `continue`, or `goto`
- Convert predicate loops into predicate functions
- Use range-based for loops wherever possible

### Memory & Performance
- Prefer preincrement (`++i`) over postincrement (`i++`)
- Cache `end()` iterators: `for (auto I = C.begin(), E = C.end(); I != E; ++I)`
- Use `auto` judiciously - only when it improves readability
- Beware unnecessary copies with `auto` - use `auto &` or `auto *`

### Error Handling & Assertions
- Assert liberally with descriptive messages: `assert(condition && "explanation")`
- Use `[[maybe_unused]]` for variables only used in assertions
- Treat compiler warnings as errors

## Specific Prohibitions

### Forbidden Practices
- **Never use**: `using namespace std`
- **Never use**: `#include <iostream>` in library files
- **Never use**: `std::endl` (use `'\n'` instead)
- **Never use**: Static constructors/destructors
- **Never use**: Default labels in fully covered enum switches
- **Never use**: `inline` keyword in class definitions (implicit)

### Namespace Rules
- Use namespace qualifiers for out-of-line function implementations
- Don't open namespace blocks in source files
- Implementation files may use `using namespace llvm;`

### Class Design
- Use `struct` only when all members are public
- Provide virtual method anchors for classes in headers
- Restrict visibility with `private`/`protected`/`static`/anonymous namespaces

## Code Examples

### Good Practices
```cpp
// Early exit pattern
Value *doSomething(Instruction *I) {
  if (I->isTerminator())
    return nullptr;
  if (!I->hasOneUse())
    return nullptr;
  
  // Main logic here
}

// Proper auto usage
for (const auto &Val : Container) observe(Val);
for (auto *Ptr : Container) Ptr->change();

// Namespace implementation
int llvm::foo(const char *s) {
  // implementation
}
```

### Error Messages
- Start with lowercase, no ending period
- Provide context: `"file.o: section header 3 is corrupt. Size is 10 when it should be 20"`

## Additional Rules

### Lambda Formatting
```cpp
// Single lambda - drop to standard indent
std::sort(foo.begin(), foo.end(), [&](Foo a, Foo b) -> bool {
  if (a.blah < b.blah)
    return true;
  return a.bam < b.bam;
});

// Multiple lambdas - indent from []
dyn_switch(V->stripPointerCasts(),
           [] (PHINode *PN) {
             // process phis...
           },
           [] (SelectInst *SI) {
             // process selects...
           });
```

### Constructor Calls
- Use parentheses for constructor calls: `Foo("name")`
- Use braces for aggregate initialization: `bar_map.insert({my_key, my_value})`
- Use equals with braced initialization: `int data[] = {0, 1, 2, 3};`

### Switch Statements
- No default labels in fully covered enum switches
- Use `llvm_unreachable()` after covered switches when building with GCC

### Loop Patterns
```cpp
// Preferred: cache end iterator
for (auto I = BB->begin(), E = BB->end(); I != E; ++I)
  // use I

// Range-based when possible
for (Instruction &I : *BB)
  // use I

// Avoid: re-evaluating end()
for (auto I = BB->begin(); I != BB->end(); ++I) // Don't do this
```

### Header Dependencies
- Include as little as possible in headers
- Use forward declarations when possible
- Self-contained headers (include all dependencies)
- Keep internal headers private

## Application Instructions
When generating LLVM code:
1. Apply these standards consistently
2. Prioritize readability and maintainability
3. Use descriptive names and clear logic flow
4. Include appropriate assertions and error handling
5. Follow the formatting rules precisely
6. Prefer LLVM utilities over standard library equivalents
7. Use early exits to reduce nesting
8. Cache iterators and avoid repeated expensive calls
9. Apply proper const-correctness and reference usage
10. Ensure headers are self-contained and minimize dependencies
