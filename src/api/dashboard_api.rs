use std::sync::Arc;
use tonic::{async_trait, Request, Response, Status};
use crate::api::proto::dashboard::dashboard_server::Dashboard;
use crate::api::proto::dashboard::{DashboardRequest, DashboardResponse};
use crate::avored_state::AvoRedState;
use crate::extensions::tonic_request::TonicRequest;
use crate::models::admin_user_model::AdminUserModelExtension;

pub struct DashboardApi {
    pub state: Arc<AvoRedState>,
}

#[async_trait]
impl Dashboard for DashboardApi {
    async fn dashboard(&self, request: Request<DashboardRequest>) -> Result<Response<DashboardResponse>, Status> {
        
        println!("->> {:<12} - dashboard", "gRPC_Dashboard_Api_Service");

        let claims = request.get_token_claim()?;
        // Use secure permission checking that validates against database
        claims
            .check_permission_secure(
                &self.state.admin_user_service,
                String::from("dashboard"),
                &self.state.db,
            )
            .await?;


        let reply = DashboardResponse { status: true };
        Ok(Response::new(reply))
    }
}