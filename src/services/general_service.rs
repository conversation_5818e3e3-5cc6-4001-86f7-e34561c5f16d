use crate::api::proto::admin_user::AdminUserModel as GrpcAdminUserModel;
use crate::api::proto::general::{LoggedInUserResponse};
use crate::models::token_claim_model::TokenClaims;

pub struct GeneralService {
    
}

impl GeneralService {
    pub async fn logged_in_user(
        &self,
        claims: TokenClaims,
    ) -> crate::error::Result<LoggedInUserResponse> {
        // Create GrpcAdminUserModel from TokenClaims data
        let model = GrpcAdminUserModel {
            id: claims.sub,
            full_name: claims.name,
            email: claims.email,
            is_super_admin: claims.is_super_admin,
            profile_image: String::new(), // Not available in JWT claims for security
            created_at: None, // Not available in JWT claims
            updated_at: None, // Not available in JWT claims
            created_by: String::new(), // Not available in JWT claims
            updated_by: String::new(), // Not available in JWT claims
            roles: vec![], // Role details not in JWT for security, only identifiers
        };

        let logged_in_user = LoggedInUserResponse {
            status: true,
            data: Some(model)
        };

        Ok(logged_in_user)
    }
}

impl GeneralService {
    pub fn new() -> crate::error::Result<GeneralService> {
        Ok(GeneralService {})
    }
}
