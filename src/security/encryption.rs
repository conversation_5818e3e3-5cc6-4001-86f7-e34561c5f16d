use crate::error::{<PERSON>rror, Result};
use rand::Rng;
use std::env;

/// Secure encryption utilities for sensitive data
pub struct EncryptionService {
    master_key: Vec<u8>,
}

impl EncryptionService {
    /// Initialize encryption service with master key from environment
    pub fn new() -> Result<Self> {
        let master_key = Self::get_or_generate_master_key()?;
        Ok(Self { master_key })
    }

    /// Get master key from environment or generate a new one
    fn get_or_generate_master_key() -> Result<Vec<u8>> {
        match env::var("AVORED_MASTER_KEY") {
            Ok(key_hex) => {
                // Decode hex string to bytes
                hex::decode(key_hex)
                    .map_err(|_| Error::Generic("Invalid master key format".to_string()))
            }
            Err(_) => {
                // Generate a new 256-bit key
                let key: Vec<u8> = (0..32).map(|_| rand::thread_rng().gen()).collect();
                let key_hex = hex::encode(&key);
                
                eprintln!("WARNING: No AVORED_MASTER_KEY found in environment.");
                eprintln!("Generated new master key: {}", key_hex);
                eprintln!("Please add this to your environment variables:");
                eprintln!("AVORED_MASTER_KEY={}", key_hex);
                
                Ok(key)
            }
        }
    }

    /// Encrypt sensitive data using AES-256-GCM
    pub fn encrypt(&self, plaintext: &str) -> Result<String> {
        use aes_gcm::{Aes256Gcm, Key, Nonce, AeadCore, AeadInPlace, KeyInit};
        
        let key = Key::<Aes256Gcm>::from_slice(&self.master_key);
        let cipher = Aes256Gcm::new(key);
        let nonce = Aes256Gcm::generate_nonce(&mut rand::thread_rng());
        
        let mut buffer = plaintext.as_bytes().to_vec();
        let tag = cipher
            .encrypt_in_place_detached(&nonce, b"", &mut buffer)
            .map_err(|_| Error::Generic("Encryption failed".to_string()))?;
        
        // Combine nonce + ciphertext + tag
        let mut result = nonce.to_vec();
        result.extend_from_slice(&buffer);
        result.extend_from_slice(&tag);
        
        Ok(base64::encode(result))
    }

    /// Decrypt sensitive data using AES-256-GCM
    pub fn decrypt(&self, ciphertext: &str) -> Result<String> {
        use aes_gcm::{Aes256Gcm, Key, Nonce, AeadInPlace, KeyInit};
        
        let data = base64::decode(ciphertext)
            .map_err(|_| Error::Generic("Invalid ciphertext format".to_string()))?;
        
        if data.len() < 28 {
            return Err(Error::Generic("Ciphertext too short".to_string()));
        }
        
        let key = Key::<Aes256Gcm>::from_slice(&self.master_key);
        let cipher = Aes256Gcm::new(key);
        
        let nonce = Nonce::from_slice(&data[0..12]);
        let tag = &data[data.len()-16..];
        let mut buffer = data[12..data.len()-16].to_vec();
        
        cipher
            .decrypt_in_place_detached(nonce, b"", &mut buffer, tag.into())
            .map_err(|_| Error::Generic("Decryption failed".to_string()))?;
        
        String::from_utf8(buffer)
            .map_err(|_| Error::Generic("Invalid UTF-8 in decrypted data".to_string()))
    }

    /// Generate a cryptographically secure random key
    pub fn generate_secure_key(length: usize) -> String {
        use rand::distributions::Alphanumeric;
        
        rand::thread_rng()
            .sample_iter(&Alphanumeric)
            .take(length)
            .map(char::from)
            .collect()
    }

    /// Hash sensitive data for storage (one-way)
    pub fn hash_sensitive_data(&self, data: &str) -> Result<String> {
        use sha2::{Sha256, Digest};
        
        let mut hasher = Sha256::new();
        hasher.update(data.as_bytes());
        hasher.update(&self.master_key);
        
        Ok(hex::encode(hasher.finalize()))
    }
}

/// Secure configuration management
pub struct SecureConfig {
    encryption: EncryptionService,
}

impl SecureConfig {
    pub fn new() -> Result<Self> {
        Ok(Self {
            encryption: EncryptionService::new()?,
        })
    }

    /// Get encrypted configuration value
    pub fn get_encrypted_env(&self, key: &str) -> Result<String> {
        let encrypted_value = env::var(key)
            .map_err(|_| Error::Generic(format!("Environment variable {} not found", key)))?;
        
        self.encryption.decrypt(&encrypted_value)
    }

    /// Set encrypted configuration value (for setup/migration)
    pub fn set_encrypted_env(&self, key: &str, value: &str) -> Result<String> {
        self.encryption.encrypt(value)
    }

    /// Validate that all required encrypted environment variables are present
    pub fn validate_encrypted_config(&self) -> Result<()> {
        let required_encrypted_vars = [
            "AVORED_JWT_SECRET_ENCRYPTED",
            "AVORED_PASSWORD_SALT_ENCRYPTED",
            "SMTP_PASSWORD_ENCRYPTED",
        ];

        for var in &required_encrypted_vars {
            if env::var(var).is_err() {
                return Err(Error::Generic(format!(
                    "Required encrypted environment variable {} not found", var
                )));
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encryption_roundtrip() {
        let service = EncryptionService::new().unwrap();
        let plaintext = "sensitive data";
        
        let encrypted = service.encrypt(plaintext).unwrap();
        let decrypted = service.decrypt(&encrypted).unwrap();
        
        assert_eq!(plaintext, decrypted);
    }

    #[test]
    fn test_secure_key_generation() {
        let key1 = EncryptionService::generate_secure_key(32);
        let key2 = EncryptionService::generate_secure_key(32);
        
        assert_eq!(key1.len(), 32);
        assert_eq!(key2.len(), 32);
        assert_ne!(key1, key2);
    }
}
