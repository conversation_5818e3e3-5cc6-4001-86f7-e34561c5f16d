// Removed unused import: use crate::models::admin_user_model::AdminUserModel;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct TokenClaims {
    pub sub: String,
    pub name: String,
    pub email: String,
    pub is_super_admin: bool,
    pub roles: Vec<String>, // Only role identifiers, not full role objects
    pub iat: usize,
    pub exp: usize,
}

impl TokenClaims {
    /// Convert TokenClaims to LoggedInUser for secure permission checking
    pub fn to_logged_in_user(&self) -> LoggedInUser {
        LoggedInUser {
            id: self.sub.clone(),
            name: self.name.clone(),
            email: self.email.clone(),
            is_super_admin: self.is_super_admin,
            roles: self.roles.clone(),
            demo_data_status: false, // Default value
        }
    }

    /// Secure permission check that validates against database instead of JWT claims
    pub async fn check_permission_secure(
        &self,
        admin_user_service: &crate::services::admin_user_service::AdminUserService,
        permission_identifier: String,
        db: &crate::providers::avored_database_provider::DB,
    ) -> crate::error::Result<()> {
        let logged_in_user = self.to_logged_in_user();
        logged_in_user
            .check_permission_secure(admin_user_service, permission_identifier, db)
            .await
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LoggedInUser {
    pub id: String,
    pub name: String,
    pub email: String,
    pub is_super_admin: bool,
    pub roles: Vec<String>,
    pub demo_data_status: bool,
}

impl LoggedInUser {
    /// Secure permission check that validates against database instead of JWT claims
    pub async fn has_permission_secure(
        &self,
        admin_user_service: &crate::services::admin_user_service::AdminUserService,
        permission_identifier: String,
        db: &crate::providers::avored_database_provider::DB,
    ) -> crate::error::Result<bool> {
        admin_user_service
            .has_permission_secure(&self.id, permission_identifier, db)
            .await
    }

    /// Secure permission check that throws error if permission denied
    pub async fn check_permission_secure(
        &self,
        admin_user_service: &crate::services::admin_user_service::AdminUserService,
        permission_identifier: String,
        db: &crate::providers::avored_database_provider::DB,
    ) -> crate::error::Result<()> {
        let has_permission = self
            .has_permission_secure(admin_user_service, permission_identifier.clone(), db)
            .await?;

        if !has_permission {
            return Err(crate::error::Error::Unauthorizeed(permission_identifier));
        }

        Ok(())
    }
}
