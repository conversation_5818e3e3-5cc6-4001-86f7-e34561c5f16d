use std::env;
use jsonwebtoken::{decode, Decoding<PERSON>ey, Validation, Algorithm};
use tonic::{Request, Status};
use crate::error::Error;
use crate::models::token_claim_model::TokenClaims;

#[allow(clippy::result_large_err)]
pub fn check_auth(mut req: Request<()>) -> Result<Request<()>, Status> {

    match req.metadata().get("authorization") {
        Some(t) => {
            let auth_value = t.to_str()
                .map_err(|_e| Status::unavailable("authorization header value is not valid string"))?;

            let jwt_token = &env::var("AVORED_JWT_SECRET")
                    .map_err(|_| Error::ConfigMissing("AVORED_JWT_SECRET".to_string()))?;
            let token = auth_value.strip_prefix("Bearer ")
                .ok_or_else(|| Status::unauthenticated("Invalid authorization format"))?;

            // Reject empty tokens
            if token.is_empty() {
                return Err(Status::unauthenticated("Empty token provided"));
            }

            // Create secure validation with restricted algorithm
            let mut validation = Validation::new(Algorithm::HS256);
            validation.validate_exp = true;
            validation.validate_exp = true;
            validation.leeway = 0; // No leeway for token expiration

            let claims = decode::<TokenClaims>(
                token,
                &DecodingKey::from_secret(jwt_token.as_ref()),
                &validation,
            ).map_err(|_| {
                Status::unauthenticated("Invalid or expired token")
            })?.claims;
            req.extensions_mut().insert(claims);

            Ok(req)
        },
        _ => Err(Status::unauthenticated("No valid auth token")),
    }
}
