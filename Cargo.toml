[package]
name = "avored-rust-cms"
version = "0.1.0"
edition = "2021"


[dependencies]
axum = { version = "0.8.4", features = ["multipart", "http2"] }
prost = "0.13.5"
prost-types = "0.13.5"
tokio = { version = "1.45.1", features = ["macros", "rt-multi-thread", "net", "fs", "io-util"] }
tonic = { version = "0.13.1" }
axum_tonic = "0.4.0"
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "fmt"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
surrealdb = { version = "2.3.3", features = ["kv-rocksdb", "kv-mem"] }
# Additional dependencies not in workspace
argon2 = "0.5.3"
jsonwebtoken = "9.3.1"
chrono = { version = "0.4.41", features = [] }
email_address = "0.2.9"
rust-i18n = "3.1.5"
rand = { version = "0.8.5", features = ["std"] }
dotenvy = "0.15.7"
tower-http = { version = "0.6.4", features = ["fs", "cors"] }
lettre = { version = "0.11.16", features = ["tokio1-native-tls"] }
handlebars = "6.3.2"
# Security dependencies
aes-gcm = "0.10.3"
base64 = "0.22.1"
hex = "0.4.3"
sha2 = "0.10.8"


[build-dependencies]
tonic-build = { version = "0.13.1", features = ["prost"] }

[profile.dev-fast]
inherits = "dev"
opt-level = 1
debug = false
incremental = true
codegen-units = 256

[profile.release-fast]
inherits = "release"
opt-level = 2
debug = false
lto = "thin"
codegen-units = 1
panic = "abort"
